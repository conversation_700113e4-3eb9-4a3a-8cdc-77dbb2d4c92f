import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Store } from '@tauri-apps/plugin-store';
import { invoke } from '@tauri-apps/api/core';
import {
  Message,
  ChatSession,
  ChatSessionSummary,
  MultiChatState,
  ChatActions,
  ChatSessionActions,
  LLMProvider,
  PluginResult
} from '../types';

// Generate unique ID for messages and chats
const generateId = () => `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Tauri store instance for chat persistence
let chatStore: Store | null = null;

const initChatStore = async () => {
  if (!chatStore) {
    try {
      chatStore = new Store('chats.json');
    } catch (error) {
      console.warn('Failed to initialize chat store, using localStorage fallback:', error);
    }
  }
  return chatStore;
};

interface EnhancedChatState extends MultiChatState {
  isInitialized: boolean;
  lastSyncTime: Date | null;
}

interface EnhancedChatActions extends ChatActions, ChatSessionActions {
  // Enhanced session management
  initializeStore: () => Promise<void>;
  syncWithTauriStore: () => Promise<void>;
  createNewChatWithTitle: (title: string) => Promise<string>;
  bulkImportChats: (chats: ChatSession[]) => Promise<void>;
  searchChats: (query: string) => ChatSessionSummary[];
  getRecentChats: (limit?: number) => ChatSessionSummary[];
  
  // Enhanced message management
  addMessageWithMetadata: (
    content: string, 
    role: 'user' | 'assistant', 
    metadata?: { model?: string; provider?: LLMProvider; tokens?: number }
  ) => void;
  
  // Plugin integration
  executePluginWithContext: (input: string, context?: any) => Promise<PluginResult | null>;
  
  // Export/Import functionality
  exportAllChats: () => Promise<string>;
  importChatsFromJson: (jsonData: string) => Promise<void>;
}

interface EnhancedChatStore extends EnhancedChatState, EnhancedChatActions {}

export const useEnhancedChatStore = create<EnhancedChatStore>()(
  persist(
    (set, get) => ({
      // State
      messages: [],
      isLoading: false,
      error: null,
      currentInput: '',
      activeChatId: null,
      chatSessions: {},
      chatSummaries: [],
      isInitialized: false,
      lastSyncTime: null,

      // Initialize store
      initializeStore: async () => {
        try {
          console.log('🔄 Initializing enhanced chat store...');

          // Try to load from Tauri store (non-blocking)
          try {
            const store = await initChatStore();
            if (store) {
              const savedChats = await store.get<Record<string, ChatSession>>('chat-sessions');
              const savedSummaries = await store.get<ChatSessionSummary[]>('chat-summaries');

              if (savedChats) {
                set({ chatSessions: savedChats });
              }

              if (savedSummaries) {
                set({ chatSummaries: savedSummaries });
              }
            }
          } catch (storeError) {
            console.warn('⚠️ Tauri store loading failed, continuing with initialization:', storeError);
          }

          // Try to load from backend (non-blocking)
          try {
            await get().loadChatSessions();
          } catch (backendError) {
            console.warn('⚠️ Backend loading failed, continuing with initialization:', backendError);
          }

          // Always set initialized to true to prevent input blocking
          set({
            isInitialized: true,
            lastSyncTime: new Date()
          });

          console.log('✅ Enhanced chat store initialized');
        } catch (error) {
          console.error('❌ Failed to initialize chat store:', error);
          // Even if there's an error, set initialized to true to prevent input blocking
          set({
            isInitialized: true,
            error: `Initialization warning: ${error}`
          });
        }
      },

      // Sync with Tauri store
      syncWithTauriStore: async () => {
        try {
          const store = await initChatStore();
          if (store) {
            const state = get();
            await store.set('chat-sessions', state.chatSessions);
            await store.set('chat-summaries', state.chatSummaries);
            await store.save();
            set({ lastSyncTime: new Date() });
          }
        } catch (error) {
          console.error('Failed to sync with Tauri store:', error);
        }
      },

      // Create new chat with custom title
      createNewChatWithTitle: async (title: string): Promise<string> => {
        const chatId = `chat_${generateId()}`;
        const now = new Date();
        
        const newSession: ChatSession = {
          id: chatId,
          title,
          messages: [],
          createdAt: now,
          updatedAt: now,
          metadata: {
            model: 'gemma3n:latest',
            tokenCount: 0,
            messageCount: 0,
            lastActivity: now,
            tags: [],
            isArchived: false
          }
        };

        const newSummary: ChatSessionSummary = {
          id: chatId,
          title,
          lastMessage: null,
          messageCount: 0,
          createdAt: now,
          updatedAt: now,
          lastActivity: now
        };

        set((state) => ({
          chatSessions: {
            ...state.chatSessions,
            [chatId]: newSession
          },
          chatSummaries: [newSummary, ...state.chatSummaries],
          activeChatId: chatId,
          messages: []
        }));

        // Sync with stores
        await get().syncWithTauriStore();
        
        // Try to create in backend as well
        try {
          await invoke('create_chat_session', { title });
        } catch (error) {
          console.warn('Backend unavailable for chat creation:', error);
        }

        return chatId;
      },

      // Search chats
      searchChats: (query: string): ChatSessionSummary[] => {
        const state = get();
        const lowercaseQuery = query.toLowerCase();
        
        return state.chatSummaries.filter(summary => 
          summary.title.toLowerCase().includes(lowercaseQuery) ||
          (summary.lastMessage && summary.lastMessage.toLowerCase().includes(lowercaseQuery))
        );
      },

      // Get recent chats
      getRecentChats: (limit = 10): ChatSessionSummary[] => {
        const state = get();
        return state.chatSummaries
          .sort((a, b) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime())
          .slice(0, limit);
      },

      // Enhanced message adding with metadata
      addMessageWithMetadata: (
        content: string, 
        role: 'user' | 'assistant', 
        metadata?: { model?: string; provider?: LLMProvider; tokens?: number }
      ) => {
        const state = get();
        const activeChatId = state.activeChatId;

        if (!activeChatId) {
          console.warn('❌ No active chat session for adding message');
          return;
        }

        const newMessage: Message = {
          id: generateId(),
          content,
          role,
          timestamp: new Date(),
        };

        set((state) => {
          const currentSession = state.chatSessions[activeChatId];
          if (!currentSession) {
            return state;
          }

          const updatedSession = {
            ...currentSession,
            messages: [...currentSession.messages, newMessage],
            updatedAt: new Date(),
            metadata: {
              ...currentSession.metadata,
              messageCount: currentSession.messages.length + 1,
              lastActivity: new Date(),
              model: metadata?.model || currentSession.metadata?.model,
              tokenCount: (currentSession.metadata?.tokenCount || 0) + (metadata?.tokens || 0)
            }
          };

          // Update summary
          const updatedSummaries = state.chatSummaries.map(summary =>
            summary.id === activeChatId
              ? {
                  ...summary,
                  lastMessage: content.substring(0, 100),
                  messageCount: updatedSession.messages.length,
                  updatedAt: new Date(),
                  lastActivity: new Date()
                }
              : summary
          );

          return {
            messages: [...state.messages, newMessage],
            chatSessions: {
              ...state.chatSessions,
              [activeChatId]: updatedSession
            },
            chatSummaries: updatedSummaries
          };
        });

        // Auto-sync after message addition
        setTimeout(() => get().syncWithTauriStore(), 1000);
      },

      // Export all chats
      exportAllChats: async (): Promise<string> => {
        const state = get();
        const exportData = {
          version: '1.0',
          exportedAt: new Date().toISOString(),
          chatSessions: state.chatSessions,
          chatSummaries: state.chatSummaries,
          totalChats: state.chatSummaries.length,
          totalMessages: Object.values(state.chatSessions).reduce(
            (total, session) => total + session.messages.length, 
            0
          )
        };
        
        return JSON.stringify(exportData, null, 2);
      },

      // Import chats from JSON
      importChatsFromJson: async (jsonData: string): Promise<void> => {
        try {
          const importData = JSON.parse(jsonData);
          
          if (!importData.chatSessions || !importData.chatSummaries) {
            throw new Error('Invalid chat export format');
          }

          set((state) => ({
            chatSessions: {
              ...state.chatSessions,
              ...importData.chatSessions
            },
            chatSummaries: [
              ...importData.chatSummaries,
              ...state.chatSummaries
            ]
          }));

          await get().syncWithTauriStore();
        } catch (error) {
          console.error('Failed to import chats:', error);
          throw error;
        }
      },

      // Placeholder implementations for required interface methods
      // (These will be implemented in the next phase)
      addMessage: (content: string, role: 'user' | 'assistant') => {
        get().addMessageWithMetadata(content, role);
      },

      updateMessage: (id: string, updates: Partial<Message>) => {
        // Implementation will be added
      },

      deleteMessage: (id: string) => {
        // Implementation will be added
      },

      clearMessages: () => {
        set({ messages: [] });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      setCurrentInput: (input: string) => {
        set({ currentInput: input });
      },

      // Placeholder chat session methods
      createNewChat: async (title?: string): Promise<string> => {
        return get().createNewChatWithTitle(title || `New Chat ${new Date().toLocaleString()}`);
      },

      switchToChat: async (chatId: string): Promise<void> => {
        const state = get();
        if (state.chatSessions[chatId]) {
          set({
            activeChatId: chatId,
            messages: state.chatSessions[chatId].messages || []
          });
          await get().syncWithTauriStore();
        }
      },

      renameChat: async (chatId: string, newTitle: string): Promise<void> => {
        const state = get();
        if (state.chatSessions[chatId]) {
          const updatedSession = {
            ...state.chatSessions[chatId],
            title: newTitle,
            lastActivity: new Date()
          };

          const updatedSummary = state.chatSummaries.find(s => s.id === chatId);
          if (updatedSummary) {
            updatedSummary.title = newTitle;
            updatedSummary.lastActivity = new Date();
          }

          set({
            chatSessions: {
              ...state.chatSessions,
              [chatId]: updatedSession
            },
            chatSummaries: [...state.chatSummaries]
          });

          await get().syncWithTauriStore();
        }
      },

      deleteChat: async (chatId: string): Promise<void> => {
        const state = get();
        const { [chatId]: deletedSession, ...remainingSessions } = state.chatSessions;
        const updatedSummaries = state.chatSummaries.filter(s => s.id !== chatId);

        // If deleting active chat, switch to another one or clear
        let newActiveChatId = state.activeChatId;
        if (state.activeChatId === chatId) {
          newActiveChatId = updatedSummaries.length > 0 ? updatedSummaries[0].id : null;
        }

        set({
          chatSessions: remainingSessions,
          chatSummaries: updatedSummaries,
          activeChatId: newActiveChatId,
          messages: newActiveChatId ? remainingSessions[newActiveChatId]?.messages || [] : []
        });

        await get().syncWithTauriStore();
      },

      archiveChat: async (chatId: string): Promise<void> => {
        const state = get();
        if (state.chatSessions[chatId]) {
          const updatedSession = {
            ...state.chatSessions[chatId],
            isArchived: true,
            lastActivity: new Date()
          };

          set({
            chatSessions: {
              ...state.chatSessions,
              [chatId]: updatedSession
            }
          });

          await get().syncWithTauriStore();
        }
      },

      loadChatSessions: async (): Promise<void> => {
        // Implementation will be added
      },

      saveChatSession: async (chatId: string): Promise<void> => {
        await get().syncWithTauriStore();
      },

      duplicateChat: async (chatId: string): Promise<string> => {
        const state = get();
        const originalSession = state.chatSessions[chatId];

        if (!originalSession) {
          throw new Error('Chat session not found');
        }

        const newChatId = generateId();
        const duplicatedSession: ChatSession = {
          ...originalSession,
          id: newChatId,
          title: `${originalSession.title} (Copy)`,
          createdAt: new Date(),
          lastActivity: new Date(),
          isArchived: false
        };

        const newSummary: ChatSessionSummary = {
          id: newChatId,
          title: duplicatedSession.title,
          lastActivity: duplicatedSession.lastActivity,
          messageCount: duplicatedSession.messages?.length || 0,
          createdAt: duplicatedSession.createdAt,
          isArchived: false
        };

        set({
          chatSessions: {
            ...state.chatSessions,
            [newChatId]: duplicatedSession
          },
          chatSummaries: [newSummary, ...state.chatSummaries]
        });

        await get().syncWithTauriStore();
        return newChatId;
      },

      exportChat: async (chatId: string): Promise<void> => {
        const state = get();
        const session = state.chatSessions[chatId];

        if (!session) {
          throw new Error('Chat session not found');
        }

        const exportData = {
          title: session.title,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          messages: session.messages || [],
          messageCount: session.messages?.length || 0
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        // Create download link
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `chat-${session.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      },

      generateContextAwareResponse: async (prompt: string, systemPrompt?: string): Promise<string> => {
        // Implementation will be added
        return '';
      },

      saveMessageToBackend: async (chatId: string, content: string, role: 'user' | 'assistant'): Promise<void> => {
        // Implementation will be added
      },

      executePluginWithContext: async (input: string, context?: any): Promise<PluginResult | null> => {
        // Implementation will be added
        return null;
      },

      bulkImportChats: async (chats: ChatSession[]): Promise<void> => {
        // Implementation will be added
      },
    }),
    {
      name: 'enhanced-chat-storage',
      partialize: (state) => ({
        activeChatId: state.activeChatId,
        chatSessions: state.chatSessions,
        chatSummaries: state.chatSummaries,
        currentInput: state.currentInput,
      }),
    }
  )
);
