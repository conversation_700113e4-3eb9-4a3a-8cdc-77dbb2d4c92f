import React, { useState, useRef, useEffect } from 'react';
import { Send, Mic, Wifi, WifiOff, ChevronDown, Settings, Volume2, VolumeX } from 'lucide-react';
import { cn } from '../utils/cn';
import { useAppStore } from '../stores/chatStore';
import { useSettingsStore } from '../stores/settingsStore';
import { llmRouter } from '../core/agents/llmRouter';
import { useFeatureFlags } from '../hooks/useFeatureFlags';

interface InputAreaProps {
  onSendMessage: (message: string, options?: { mode?: 'online' | 'offline'; model?: string }) => void;
  onVoiceRecord?: () => void;
  onVoiceToggle?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
  showVoiceControls?: boolean;
}

const InputAreaCore: React.FC<InputAreaProps> = ({
  onSendMessage,
  onVoiceRecord,
  onVoiceToggle,
  disabled = false,
  isLoading = false,
  placeholder = "Type your message...",
  showVoiceControls = true
}) => {
  // Store hooks must be called first
  const { settings } = useSettingsStore();
  const { llmPreferences, setPreferredProvider } = useAppStore();
  const { isVoiceEnabled: isVoiceFeaturesEnabled } = useFeatureFlags();

  // Safe initialization with default values
  const getInitialMode = (): 'online' | 'offline' => {
    if (!llmPreferences) return 'offline';
    return llmPreferences.preferredProvider === 'online' ? 'online' : 'offline';
  };

  const getInitialModel = (): string => {
    if (!llmPreferences) return 'gemma3n:latest';
    if (llmPreferences.preferredProvider === 'online') {
      return llmPreferences.selectedOnlineModel || 'gemini-2.5-flash';
    }
    return llmPreferences.selectedOfflineModel || 'gemma3n:latest';
  };

  // State initialization with safe defaults
  const [currentInput, setCurrentInput] = useState('');
  const [selectedMode, setSelectedMode] = useState<'online' | 'offline'>(getInitialMode());
  const [selectedModel, setSelectedModel] = useState(getInitialModel());
  const [showModeDropdown, setShowModeDropdown] = useState(false);
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [isTTSEnabled, setIsTTSEnabled] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isOnlineModeEnabled = true; // Always enable online mode toggle

  // Update state when llmPreferences becomes available
  useEffect(() => {
    if (llmPreferences) {
      const newMode = llmPreferences.preferredProvider === 'online' ? 'online' : 'offline';
      const newModel = newMode === 'online'
        ? llmPreferences.selectedOnlineModel || 'gemini-2.5-flash'
        : llmPreferences.selectedOfflineModel || 'gemma3n:latest';

      setSelectedMode(newMode);
      setSelectedModel(newModel);
    }
  }, [llmPreferences]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [currentInput]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowModeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSendMessage = () => {
    if (!currentInput.trim() || disabled || isLoading) return;

    const messageOptions = {
      mode: selectedMode,
      model: selectedModel
    };

    onSendMessage(currentInput.trim(), messageOptions);
    setCurrentInput('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceClick = () => {
    if (onVoiceRecord) {
      onVoiceRecord();
    }
  };

  const handleTTSToggle = () => {
    setIsTTSEnabled(!isTTSEnabled);
    if (onVoiceToggle) {
      onVoiceToggle();
    }
  };

  const handleModeChange = (mode: 'online' | 'offline', model: string) => {
    try {
      console.log('🔄 Mode change requested:', { mode, model });
      setSelectedMode(mode);
      setSelectedModel(model);
      setShowModeDropdown(false);

      // Persist the selection to app store (with error handling)
      if (setPreferredProvider) {
        setPreferredProvider(mode === 'online' ? 'online' : 'local');
        console.log('✅ Mode change persisted to store:', mode);
      } else {
        console.warn('⚠️ setPreferredProvider not available');
      }
    } catch (error) {
      console.error('❌ Error changing mode:', error);
      // Revert to previous state if error occurs
      setShowModeDropdown(false);
    }
  };

  const availableModels = {
    offline: ['gemma3n:latest', 'qwen2.5:7b', 'llama3.1:8b'],
    online: ['gemini-2.5-flash', 'gemini-2.5-pro']
  };

  // Check if we're still loading preferences
  const isLoadingPreferences = !llmPreferences;

  return (
    <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 relative z-40">
      <div className="max-w-4xl mx-auto">
        {/* Mode Selection */}
        <div className="flex items-center justify-between mb-3">
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowModeDropdown(!showModeDropdown)}
              disabled={disabled}
              className={cn(
                "flex items-center space-x-2 px-3 py-1.5 rounded-lg border text-sm font-medium transition-colors",
                "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700",
                "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",
                "focus:outline-none focus:ring-2 focus:ring-blue-500",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {selectedMode === 'online' ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-gray-500" />
              )}
              <span>{selectedMode === 'online' ? 'Online' : 'Offline'}</span>
              <span className="text-xs text-gray-500">
                ({isLoadingPreferences ? 'Loading...' : selectedModel})
              </span>
              <ChevronDown className="w-4 h-4" />
            </button>

            {showModeDropdown && (
              <div className="absolute bottom-full left-0 mb-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-[70]">
                <div className="p-2">
                  <div className="space-y-2">
                    {/* Offline Mode */}
                    <div>
                      <div className="flex items-center space-x-2 px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <WifiOff className="w-4 h-4" />
                        <span>Offline Mode</span>
                      </div>
                      {availableModels.offline.map((model) => (
                        <button
                          key={model}
                          onClick={() => handleModeChange('offline', model)}
                          className={cn(
                            "w-full text-left px-3 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700",
                            selectedMode === 'offline' && selectedModel === model && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                          )}
                        >
                          {model}
                        </button>
                      ))}
                    </div>

                    {/* Online Mode */}
                    {isOnlineModeEnabled && (
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
                        <div className="flex items-center space-x-2 px-2 py-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          <Wifi className="w-4 h-4" />
                          <span>Online Mode</span>
                        </div>
                        {availableModels.online.map((model) => (
                          <button
                            key={model}
                            onClick={() => handleModeChange('online', model)}
                            className={cn(
                              "w-full text-left px-3 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700",
                              selectedMode === 'online' && selectedModel === model && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                            )}
                          >
                            {model}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          {showVoiceControls && isVoiceFeaturesEnabled && (
            <div className="flex items-center space-x-2">
              <button
                onClick={handleTTSToggle}
                disabled={disabled}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  "hover:bg-gray-100 dark:hover:bg-gray-700",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500",
                  isTTSEnabled ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
                title={isTTSEnabled ? "Disable Text-to-Speech" : "Enable Text-to-Speech"}
              >
                {isTTSEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
              </button>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => console.log('🎯 Input area focused')}
              onBlur={() => console.log('🎯 Input area blurred')}
              placeholder={placeholder}
              disabled={disabled || isLoading}
              className={cn(
                "w-full resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2",
                "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100",
                "placeholder-gray-500 dark:placeholder-gray-400",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200",
                // Fix cursor issue - only apply not-allowed cursor when actually disabled
                (disabled || isLoading) ? "opacity-50 cursor-not-allowed" : "cursor-text"
              )}
              rows={1}
              style={{ 
                minHeight: '40px', 
                maxHeight: '120px',
                overflow: 'hidden'
              }}
            />
          </div>
          
          {/* Voice Record Button */}
          {showVoiceControls && isVoiceFeaturesEnabled && onVoiceRecord && (
            <button
              onClick={handleVoiceClick}
              disabled={disabled || isLoading}
              className={cn(
                "p-2 rounded-lg transition-colors",
                "bg-blue-600 hover:bg-blue-700 text-white",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
              title="Voice Input"
            >
              <Mic className="w-5 h-5" />
            </button>
          )}

          {/* Send Button */}
          <button
            onClick={handleSendMessage}
            disabled={!currentInput.trim() || disabled || isLoading}
            className={cn(
              "p-2 rounded-lg transition-colors",
              "bg-blue-600 hover:bg-blue-700 text-white",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
            title="Send Message"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Error boundary wrapper component
const InputArea: React.FC<InputAreaProps> = (props) => {
  try {
    return <InputAreaCore {...props} />;
  } catch (error) {
    console.error('❌ InputArea render error:', error);

    // Fallback UI when there's an error
    return (
      <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 relative z-40">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
            <div className="text-center">
              <p className="text-red-800 dark:text-red-300 text-sm font-medium">
                Input area temporarily unavailable
              </p>
              <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                Please refresh the page to restore functionality
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default InputArea;
